jQuery(document).ready(function() {

    var inputKey = [
        'adult-price',
        'male-price',
        'female-price',
        'children-price',
        'student-price',
        'infant-price',
        'person-price',
        'tour-price-text'
    ]

    if (jQuery('input[data-slug=enable-manual-conversion]:checked').val() !== 'on') {
        var jqueryStr = '';
        inputKey.forEach(item => {
            jqueryStr += 'div[data-wrapper-slug=' + item + '],';
        })
        jqueryStr = jqueryStr.substring(0, jqueryStr.length - 1);
        var arr = jQuery(jqueryStr);
        for (var x = 0; x < arr.length; x++) {
            jQuery(arr[x]).find('input[type=text]').addClass('quarter-width');
            jQuery(arr[x]).find('input[type=text]')
                .after('<input class="tourmaster-html-option-tabs-input tourmaster-html-option-text quarter-width" type="text" disabled />')
        }

        jQuery('input[data-slug="tour-price-text"]').addClass('quarter-width');
        jQuery('input[data-slug="tour-price-text"]')
            .after('<input class="tourmaster-html-options-tabs-input tourmaster-html-option-text quarter-width" type="text" disabled />')

        jQuery.ajax({
            url: ajax.ajaxurl,
            dataType: 'JSON',
            type: 'POST',
            data: {
                action: 'fetch_base_rate',
                postId: jQuery("#post_ID").val(),
            },
            success: function (response) {
                if (response.success) {
                    var rate = response.rate;
                    for (var x = 0; x < arr.length; x++) {
                        var left = jQuery(arr[x]).find('input[type=text]:first').val();
                        if (left) {
                            jQuery(arr[x]).find('input[type=text]:last').val(Math.round(rate * left));
                        }
                    }
                    left = jQuery('input[data-slug="tour-price-text"]').val();
                    jQuery('input[data-slug="tour-price-text"]').next().val(Math.round(rate * left));
                }
            }
        })
    }

})

function manualUpdate() {
    jQuery.ajax({
        url: ajax.ajaxurl,
        dataType: 'JSON',
        type: 'POST',
        data: {
            action: 'manual_update_base_rate',
        },
        success: function (response) {
            window.location.reload();
        }
    });
}

function manualToursUpdate() {
    jQuery.ajax(
        {
            url: ajax.ajaxurl,
            dataType: 'JSON',
            type: "POST",
            data: {
                action: 'manual_tours_update',
            },
            success: function (response) {
               // window.location.reload()
            }
        }
    )
}

function saveManualRate() {

    if (jQuery('.manual-exchange-rate').val().length === 0) {
        alert('Please type a base rate!');
        return;
    }

    if (isNaN(jQuery('.manual-exchange-rate').val())) {
        alert('only numeric value is accepted')
        return;
    }

    jQuery.ajax(
        {
            url: ajax.ajaxurl,
            dataType: 'JSON',
            type: "POST",
            data: {
                action: 'manual_entered_value',
                manual_exchange_rate: jQuery(".manual-exchange-rate").val(),
                manual_exchange_rate_isk: jQuery(".manual_exchange_rate_isk").val(), 
                manual_exchange_rate_nok: jQuery(".manual_exchange_rate_nok").val(), 
                manual_exchange_rate_sek: jQuery(".manual_exchange_rate_sek").val(), 
            },
            success: function (response) {
                window.location.reload()
            }
        }
    )
}
