<?php

class OpenExchangeRates
{
    /**
     * @var
     */
    private $appId;


    /**
     * @param $appId
     */
    public function __construct($appId) {
        $this->appId = $appId;
    }

    /**
     * @return mixed|null
     * @throws Exception
     */
    public function fetchBaseRate(){

        if (empty($this->appId)) {
            throw new Exception("OpenExchangeRates APP ID is required");
        }

        $oxr_url = "https://openexchangerates.org/api/latest.json?app_id=" . $this->appId;
        $ch = curl_init($oxr_url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $json = curl_exec($ch);
       
        if (curl_errno($ch) !== CURLE_OK) {
            throw new Exception(curl_error($ch));
        }
        
        curl_close($ch);
        $oxr_latest = json_decode($json);
        return $oxr_latest->rates; 
    }

    public static function extract_value($result, $currency='EUR') {
        switch ($currency) {
            case 'SEK':
                return $result->SEK;
            case 'ISK':
                return $result->ISK;
            case 'NOK':
                return $result->NOK;
            case 'EUR':
            default:
                return $result->EUR;
        }
    }

    /**
     * @param float $base
     * @param float $fromValue
     * @return float|int
     */
    public static function convert(float $base, float $fromValue) {
        return round($base * $fromValue);
    }

    /**
     * @throws Exception
     */
    public static function hitCron() {
        $apiId = tourmaster_get_option('payment', 'exchange-api-key');
        $openexchange_rate_options = get_option( 'openexchange_rate_options' ); // Array of All Options

        $instance = new static($apiId);
        try {
            $result = $instance->fetchBaseRate();

            $openexchange_rate_options['usd_eur_rate']  = round(self::extract_value($result), 2); // EUR
            $openexchange_rate_options['usd_sek_rate']  = round(self::extract_value($result, 'SEK'), 2);
            $openexchange_rate_options['usd_isk_rate']  = round(self::extract_value($result, 'ISK'), 2);
            $openexchange_rate_options['usd_nok_rate']  = round(self::extract_value($result, 'NOK'), 2);
            $openexchange_rate_options['datetime_fetched'] = strtotime('now');
            
            update_option('openexchange_rate_options', $openexchange_rate_options);
            error_log('Exchange rate saved at ' . $openexchange_rate_options['datetime_fetched']);
        } catch (Exception $e) {
            // $savedRateValue = round((float)tourmaster_get_option('payment', 'manual-exchange-rate'), 2);
            error_log("ExchangeAPI exception: " . $e->getMessage());
        }
    }
}
