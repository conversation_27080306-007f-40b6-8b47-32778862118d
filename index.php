<?php
/**
 * @package Custom Tourmaster plugin for OpenExchangeRates.org
 * @version 1.3
 */
/*
Plugin Name: Tourmaster plugin for OpenExchangeRates.org
Description:
Author: DevPlus31
Version: 1.5
Author URI: http://devplus31.com/

Edit 1.1
 - Add Manual Conversion Rate Option
Edit 1.2
 - Add currency switcher
Edit 1.3
 - use any recent rate value either fetched or manually entered

Edit 1.5
 - Winter Tours Edit
 * Update post tours values on save
 * Cronjob for auto update

Fix warning bug

*/

require_once('includes/OpenExchangeRates.php');

if (session_status() !== PHP_SESSION_ACTIVE) {
    session_start();
}

define('CUSTOM_TOURMASTER_OPENEXCHANGE_URL', plugins_url('', __FILE__));

// check_bestmeing_tours_prices();


// Function to programmatically update tour prices
if( !function_exists('tourmaster_update_tour_prices') ){
    function tourmaster_update_tour_prices($tour_id, $price_data = array()) {

        // Get existing tour options
        $tour_option = tourmaster_get_post_meta($tour_id, 'tourmaster-tour-option');
        if(empty($tour_option)) {
            $tour_option = array();
        }

        // Update main tour price fields
        if(isset($price_data['tour-price-text'])) {
            $tour_option['tour-price-text'] = $price_data['tour-price-text'];
        }
       /* if(isset($price_data['tour-price-text-second'])) {
            $tour_option['tour-price-text-second'] = $price_data['tour-price-text-second'];
        }
        if(isset($price_data['tour-price-discount-text'])) {
            $tour_option['tour-price-discount-text'] = $price_data['tour-price-discount-text'];
        }*/

        // Update date-price package data if exists
        if(!empty($tour_option['date-price']) && isset($price_data['date-price'])) {
            foreach($tour_option['date-price'] as $date_key => $date_price) {
                if(!empty($date_price['package'])) {
                    foreach($date_price['package'] as $package_key => $package) {
                        // Update package prices
                        $price_fields = array(
                            'person-price',
                            'adult-price',
                            'children-price',
                            'student-price',
                            'infant-price',
                            'male-price',
                            'female-price'
                        );

                        foreach($price_fields as $field) {
                            if(isset($price_data['date-price'][$date_key]['package'][$package_key][$field])) {
                                $tour_option['date-price'][$date_key]['package'][$package_key][$field] =
                                    $price_data['date-price'][$date_key]['package'][$package_key][$field];
                            }
                        }
                    }
                }
            }
        }

        // Save updated tour options
        update_post_meta($tour_id, 'tourmaster-tour-option', $tour_option);

        // Update the tour price meta (used for search and display)
        if(isset($price_data['tour-price-text'])) {
            update_post_meta($tour_id, 'tourmaster-tour-price', $price_data['tour-price-text']);
        }

        // Update discount status
        /*if(!empty($price_data['tour-price-discount-text'])) {
            update_post_meta($tour_id, 'tourmaster-tour-discount', 'true');
        } else if(isset($price_data['tour-price-discount-text']) && empty($price_data['tour-price-discount-text'])) {
            delete_post_meta($tour_id, 'tourmaster-tour-discount');
        }*/

        return true;
    }
}


function update_tour_prices($tour) {
    // Input validation
    if (empty($tour)) {
        error_log("Error: tour is empty");
        return false;
    }

    $currency = Custom_Tourmaster_OpenExchange::get_post_currency($tour->ID);
    if (empty($currency)) {
        error_log("Error: Unable to get currency for tour ID {$tour->ID}");
        return false;
    }

    $price_fields = array('person-price', 'adult-price', 'children-price', 'infant-price');

    $options = tourmaster_get_post_meta($tour->ID, "tourmaster-tour-option");
    if (empty($options) || !is_array($options)) {
        error_log("Warning: No options found for tour ID {$tour->ID}");
        return false;
    }

    $converted_date_prices = array();
    $conversion_errors = array();
    $main_tour_price = '';

    // Process and convert all prices
    if (!empty($options['date-price'])) {
        foreach ($options['date-price'] as $date_key => $date_price) {
            if (!empty($date_price['package'])) {
                $converted_packages = array();

                foreach ($date_price['package'] as $package_key => $package) {
                    $converted_package = array();

                    foreach ($price_fields as $field) {
                        $second_field = $field . '-second';
                        if (isset($package[$second_field]) && !empty($package[$second_field])) {
                            try {
                                $converted_value = Custom_Tourmaster_OpenExchange::convertValue(
                                    $package[$second_field],
                                    strtolower($currency),
                                    true
                                );

                                if ($converted_value !== false) {
                                    $converted_package[$field] = $converted_value;

                                    // Set main tour price from first adult price found
                                    if (empty($main_tour_price) && $field === 'adult-price') {
                                        $main_tour_price = $converted_value;
                                    }
                                }

                            } catch (Exception $e) {
                                $conversion_errors[] = "Failed to convert {$field} for date {$date_key}, package {$package_key}: " . $e->getMessage();
                            }
                        }
                    }

                    // Only add package if it has converted prices
                    if (!empty($converted_package)) {
                        $converted_packages[$package_key] = $converted_package;
                    }
                }

                // Only add date if it has converted packages
                if (!empty($converted_packages)) {
                    $converted_date_prices[$date_key] = array('package' => $converted_packages);
                }
            }
        }
    }

    // Log any conversion errors
    if (!empty($conversion_errors)) {
        error_log("Currency conversion warnings for tour ID {$tour->ID}: " . implode('; ', $conversion_errors));
    }

    // Ensure we have converted data
    if (empty($converted_date_prices)) {
        error_log("Warning: No prices could be converted for tour ID {$tour->ID}");
        return false;
    }

    // If no main tour price was set from adult-price, try person-price
    if (empty($main_tour_price)) {
        foreach ($converted_date_prices as $date_price) {
            if (!empty($date_price['package'])) {
                foreach ($date_price['package'] as $package) {
                    if (isset($package['person-price'])) {
                        $main_tour_price = $package['person-price'];
                        break 2;
                    }
                }
            }
        }
    }

    // Return only converted values
    return array(
        'tour-price-text' => $main_tour_price,
        'date-price' => $converted_date_prices
    );
}


function __update_bestmeing_tours_prices()  {
    $bestmmeing_terms = get_terms(array(
        'taxonomy' => 'bestemming',
        'hide_empty' => false,
        'meta_query' => array(
            array(
                'key' => 'currency',
                'compare' => 'EXISTS'
            )
        )
    ));

    if (is_wp_error($bestmmeing_terms) || empty($bestmmeing_terms)) {
        error_log('No terms found in bestemming taxonomy or error occurred');
        return;
    }

    foreach ($bestmmeing_terms as $term) {
        // Get posts (tours) associated with this taxonomy term
        $tours = get_posts(array(
            'post_type' => 'tour', // Change to your tour post type if different
            'posts_per_page' => -1,
            'tax_query' => array(
                array(
                    'taxonomy' => 'bestemming',
                    'field'    => 'term_id',
                    'terms'    => $term->term_id,
                ),
            ),
        ));

        foreach ($tours as $tour) {
            // Fetch the adult-price custom field
            $price_data = update_tour_prices($tour);
            tourmaster_update_tour_prices($tour->ID, $price_data);
        }
    }
}

function check_bestmeing_tours_prices() {
    // Get all terms from the 'bestmmeing' taxonomy
    // die('Here');
    add_action('registered_taxonomy', function ($taxonomy, $object_type, $args) {
        if ($taxonomy === 'bestemming') {
            __update_bestmeing_tours_prices();
        }
     }, 10, 3);
}
class Custom_Tourmaster_OpenExchange
{
    /**
     * OpenExchangeRates.org App ID
     **/
    private $apiAppId;

    /**
     * @var
     */
    private $openexchange_rate_options;


    public function __construct() {
        add_action( 'wp_loaded', [$this, 'register_all_scripts'] );
        add_action('wp_ajax_fetch_base_rate', [$this, 'fetch_base_rate']);
        add_action('wp_ajax_nopriv_fetch_base_rate', [$this, 'fetch_base_rate']);
        add_action('wp_ajax_manual_update_base_rate', [$this, 'update_base_rate']);
        add_action('wp_ajax_manual_entered_value', [$this, 'manual_entered_value']);
        add_action('wp_ajax_nopriv_manual_entered_value', [$this, 'manual_entered_value']);
        add_action('wp_ajax_switch_currency_action', array($this, 'ajax_switch_currency_handler'));
        add_action('wp_ajax_nopriv_switch_currency_action', array($this, 'ajax_switch_currency_handler'));

        add_action('tourmaster_after_ajax_save_page_option', array($this, 'update_prices_rates'));

        add_action('wp_ajax_manual_tours_update', array($this, 'manual_tours_update'));
        add_action('wp_ajax_nopriv_manual_tours_update', array($this, 'manual_tours_update'));

        add_action('save_post', function ($post_id, $post, $update) {
            // Prevent running on autosave, ajax, etc.
            if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) return;
            if (wp_is_post_autosave($post_id) || wp_is_post_revision($post_id)) return;

            // Optional: limit to specific post type (e.g., 'tour')
            if ($post->post_type !== 'tour') return;

            // Your custom logic here
            // error_log("✅ Post '{$post->post_title}' was updated (ID: $post_id)");

            $this->update_prices_rates($post_id);

        }, 10, 3);

        add_action('wp_get_nav_menu_items', array($this, 'currency_switch_menu'), 10, 3);

        add_action( 'admin_menu', array( $this, 'openexchange_rate_add_plugin_page' ) );
        add_action( 'admin_init', array( $this, 'openexchange_rate_page_init' ) );
        add_shortcode('price_usd_eur', array($this, 'add_price_usd_eur_shortcode'));

        add_shortcode('price_isk_eur', array($this, 'add_price_isk_eur_shortcode'));
        add_shortcode('price_nok_eur', array($this, 'add_price_nok_eur_shortcode'));
        add_shortcode('price_sek_eur', array($this, 'add_price_sek_eur_shortcode'));
        add_shortcode('currency-switcher', array($this, 'tourmaster_currency_switcher'));

        add_filter('get_post_metadata', array($this, 'modify_post_meta_out'), 10, 3);

       //  add_filter('init', array($this, 'register_menu_currency'));

        $this->register_menu_currency();
       // 
       //  OpenExchangeRates::hitCron();
    }

    public function register_menu_currency() {
        $args = array(
            'exclude_from_search'   => true,
            'publicly_queryable'    => false,
            'show_ui'               => true,
            'show_in_nav_menus'     => true,
            'show_in_menu'          => false,
            'show_in_admin_bar'     => false,
            'can_export'            => false,
            'public'                => false,
            'label'                 => 'Currency Switcher'
        );
        register_post_type( 'currency_switcher', $args );
    }

    public function create_menu_entries()
    {
        $posts = get_posts( array( 'post_type' => 'currency_switcher',  'posts_per_page'   => -1  ) );
        $currencies = [ 'usd' => 'USD', 'eur' => 'EUR', 'current_currency' => 'Current currency', 'opposite_currency' => 'Opposite currency'];
        foreach ($currencies as $currency_code => $currency_label) {
            $existing_curr = null;
            foreach ($posts as $post) {
                if ($post->post_content == $currency_code) {
                    $existing_curr = $post;
                    break;
                }
            }

            $p = array(
                'post_title' => $currency_label,
                'post_content' => $currency_code,
                'post_status' => 'publish',
                'post_type' => 'currency_switcher'
            );

            if ($existing_curr) {
                $p['ID'] = $existing_curr->ID;
                wp_update_post($p);
            }else {
                wp_insert_post($p);
            }
        }
    }

    public function update_prices_rates($post_id) {
        $post = get_post($post_id);
        $price_data = update_tour_prices($post);
      //  var_dump($price_data);
        if ($price_data === false) return;
        tourmaster_update_tour_prices($post_id, $price_data);
    }

    public function manual_tours_update() {
        __update_bestmeing_tours_prices();
    }

    public function add_currency_icon()
    {
        $currency_icon = plugin_dir_url( __FILE__) . 'images/switch-currency.png'; // update path
        return '<img height="28" width="28" src="' . esc_url($currency_icon) . '" class="currency-icon">';
    }

    public function currency_switch_menu($items) {

        $current_currency_set = false;
        $item_key_to_unset = false;
        foreach ($items as $key => $item) {
            if ($item->object == 'currency_switcher') {
                $cu_id = get_post_meta( $item->ID, '_menu_item_object_id', true );
                $cu_post = get_post($cu_id);
                if ( $cu_post == null || $cu_post->post_type != 'currency_switcher' ) {
                    continue;
                }

                $currency_code = $cu_post->post_content;

                if ( $currency_code == $_SESSION['current_currency']) {
                    $item_key_to_unset = $key;
                }

                if ( $currency_code == 'current_currency') {
                    $current_currency_set = true;
                    $currency_code = $_SESSION['current_currency'];
                }

                if ( $currency_code == 'opposite_currency') {
                    $currency_code = $_SESSION['current_currency'] === 'usd' ? 'eur' : $currency_code;

                    $items[$key]->classes[] = '';
                    $items[$key]->url = '#';
                    $items[$key]->title =  $this->add_currency_icon()  . ' ' . $_SESSION['current_currency'];
                }

                if ($currency_code != 'opposite_currency' && $currency_code != 'current_currency') {

                    $items[$key]->classes[] = 'currency-switcher-item';
                    $items[$key]->title = '<span data-no-translation>';

                    $items[$key]->title .= '<span class="currency-name" data-currency="'. $cu_post->post_title .'">' . $cu_post->post_title .  '</span>';
                    $items[$key]->title .= '</span>';
                }

            }
        }

        if ($current_currency_set && $item_key_to_unset) {
            unset($items[$item_key_to_unset]);
            $items = array_values( $items );
        }

        return $items;

    }


    public function schema_webpage($pieces, $arg)
    {
      //  die($pieces);

    }

    /**
     * @param $check
     * @param $object_id
     * @param $meta_key
     * @return array|mixed
     */
    public function modify_post_meta_out($check, $object_id, $meta_key) {
        
        if (isset($_SESSION['current_currency']) && $_SESSION['current_currency'] === 'USD') {
             return $check;
        }
    
        if (isset($_POST['action']) && $_POST['action'] === 'editpost' || isset($_GET['action']) && $_GET['action'] === 'edit') {
            return $check;
        }

       //  var_dump($_SESSION['current_currency']);

        // Get the actual currency for this tour from its taxonomy
        $currency = static::get_post_currency($object_id);
        if($meta_key === 'tourmaster-tour-option') {
            remove_filter('get_post_metadata', array($this, 'modify_post_meta_out'));
            $manual_conversion = get_post_meta($object_id, 'tourmaster-tour-option', true);

            if (isset($manual_conversion['enable-manual-conversion']) && $manual_conversion['enable-manual-conversion'] === 'enable') {
                $current_meta = get_post_meta($object_id, $meta_key, TRUE);
                if (isset($_SESSION['current_currency']) && $_SESSION['current_currency'] === 'EUR') {
                    $current_meta['tour-price-text'] = $current_meta['tour-price-text-second'];
                    $date_price = $current_meta['date-price'][0];
                    foreach ($date_price['package'] as $index => $package) {
                        foreach($package as $key => $item) {
                            if (str_ends_with($key, 'price') && !empty($item)) {
                                $current_meta['date-price'][0]['package'][$index][$key] = $current_meta['date-price'][0]['package'][$index][$key . '-second'];
                            }
                        }
                    }
                }

                add_filter('get_post_metadata', array($this, 'modify_post_meta_out'), 10 , 3);
                return array($current_meta);
            }

            $current_meta = get_post_meta($object_id, $meta_key, TRUE);

            if (isset($current_meta['date-price']) && isset($current_meta['date-price'][0]['package'])) {

                // Convert USD to EUR using the same logic as convertValue method
                if (isset($_SESSION['current_currency']) && $_SESSION['current_currency'] === 'EUR') {
                    // Get the tour's original currency
                    $tour_currency = static::get_post_currency($object_id);

                    // Convert main tour price using convertValue method for consistency
                    if (!empty($current_meta['tour-price-text'])) {
                        $converted_value = static::convertValue($current_meta['tour-price-text'], 'usd', false);
                        $current_meta['tour-price-text'] = (string)$converted_value;
                    }

                    // Convert package prices using convertValue method
                    if (isset($current_meta['date-price'][0]['package'])) {
                        $date_price = $current_meta['date-price'][0];
                        foreach ($date_price['package'] as $index => $package) {
                            foreach($package as $key => $item) {
                                if (str_ends_with($key, 'price') && !empty($item)) {
                                    $converted_value = static::convertValue($item, 'usd', false);
                                    $current_meta['date-price'][0]['package'][$index][$key] = (string)$converted_value;
                                }
                            }
                        }
                    }
                }

                add_filter('get_post_metadata', array($this, 'modify_post_meta_out'), 10 , 3);
                return array($current_meta);
            }
        }

        return $check;
    }

    public static function get_post_currency($post_id, $taxonomy_slug = 'bestemming') {
        // Get the terms associated with the post
        $terms = get_the_terms($post_id, $taxonomy_slug);

        if (!empty($terms) && !is_wp_error($terms)) {
            $term = $terms[0];
            $currency = get_term_meta($term->term_id, 'currency', true);
            return !empty($currency) ? $currency : 'EUR';
        }

        return 'EUR'; // Return default if no terms found
    }

    public function ajax_switch_currency_handler()
    {
        $currency = $_POST['currency'] == 'USD' ? 'USD' : 'EUR';
        // do_action( 'litespeed_purge_all' );
        if ($_POST['currency'] === 'toggle') { 
            $currency = $_SESSION['current_currency'] == 'USD' ? 'EUR' : 'USD'; 
        }

        $_SESSION['currency_change_requested'] = true;
        $_SESSION['current_currency'] = $currency;

        die(json_encode(array('success' => true)));
    }

    public function tourmaster_currency_switcher($atts){
		ob_start();
        $currency = tourmaster_get_option('general', 'tour-schema-price-currency');
        global $TRP_LANGUAGE;

        if (!isset($_SESSION['currency_change_requested'])) {
            $_SESSION['current_currency'] = $TRP_LANGUAGE === 'en_US' ? 'USD' : 'EUR';
        }

        if (isset($_SESSION['current_currency'])) { $currency = $_SESSION['current_currency'] == 'USD' ? '$' : '€'; }
		?>
		<div class="tour_currency_switcher_shortcode">
				<div class="tour-currency-switcher-container currency_switcher_shortcode">
					<select style="padding: 11px 0px 9px 5px; border-radius: 3px; width: 8em; vertical-align: baseline;" id="currencySwitcher" onchange="currencySwitcherChanged(event)">
                        <option <?= $currency !== '$' ? 'selected' : '' ?> value="EUR">EUR</option>
						<option <?= $currency === '$' ? 'selected' : '' ?>  value="USD">USD</option>
					</select>
				</div>	
			</div>
        <script>
            function currencySwitcherChanged(event, object = true) {
                
                jQuery.ajax({
                    type: 'POST',
                    url: window.ajaxurl,
                    data: {
                        action: 'switch_currency_action',
                        currency: event === 'toggle' ? 'toggle' : (object) ? event.target.value : event
                    },
                    dataType: 'json',
                    error: function(a, b, c){
                        console.log(a, b, c);
                    },
                    success: function(data){
                       location.reload();
                    }
                });
            }

        </script>
		<?php 
		// $ret = ob_get_contents();
		return ob_get_clean();
	}


    /**
     * @param $value
     * @return float|int|void
     */
    public static function convertValue($value, $currency, $reverse = false) {
        if (is_numeric($value)) {
            if (empty($currency)) { $currency = "eur"; }
            $options =  get_option('openexchange_rate_options');
            $currency = strtolower($currency);

            // Get the current session currency to determine target conversion
            $target_currency = isset($_SESSION['current_currency']) ? strtolower($_SESSION['current_currency']) : 'eur';

            if ($currency === $target_currency) {
                // No conversion needed if source and target are the same
                return (float)$value;
            }

            // Handle USD to EUR conversion
            if ($currency === 'usd' && $target_currency === 'eur') {
                $eur_rate = $options['usd_eur_rate'];
                return round((float)$value * $eur_rate);
            }

            // Handle EUR to USD conversion
            if ($currency === 'eur' && $target_currency === 'usd') {
                $eur_rate = $options['usd_eur_rate'];
                return round((float)$value / $eur_rate);
            }

            $base_rate = $options["usd_{$currency}_rate"];

            if ($reverse && in_array($currency, ['isk', 'sek', 'nok'])) {
                // Convert from source currency to USD first
                $usd_value = (1 / $base_rate) * (float)$value;

                // If target is EUR, convert USD to EUR
                if ($target_currency === 'eur') {
                    $eur_rate = $options['usd_eur_rate'];
                    return round($usd_value * $eur_rate);
                }

                // If target is USD, return USD value
                return round($usd_value);
            }

            return OpenExchangeRates::convert($base_rate, (float)$value);
        }
    }

    /**
     * @return void
     * @throws Exception
     */
    public function update_base_rate() {
        $result = OpenExchangeRates::hitCron();
       //  wp_send_json_success(['rate_usd_eur' => $result]);
        die;
    }

    public function manual_entered_value() {

        if (isset($_POST['manual_exchange_rate']) && is_numeric($_POST['manual_exchange_rate'])) {
            $openexchange_rate_options = get_option( 'openexchange_rate_options' ); // Array of All Options
            $openexchange_rate_options['manual_exchange_rate_updated_at'] = strtotime('now');
        
            $currencies = ['', '_sek', '_isk', '_nok']; // Empty string for EUR (no suffix)
            foreach ($currencies as $suffix) {
                $field_name = 'manual_exchange_rate' . $suffix;
                $openexchange_rate_options[$field_name] = isset($_POST[$field_name]) ? sanitize_text_field($_POST[$field_name]) : '';
            }
            
            update_option('openexchange_rate_options', $openexchange_rate_options);
        }
        die();
    }

    /**
     * @param $attrs
     * @return float
     */
    public function add_price_usd_eur_shortcode($attrs){
        if (isset($attrs['value'])) {
            $options = get_option('openexchange_rate_options');
            $base_rate = $options['usd_eur_rate'];
            $currencySign = $_SESSION['current_currency'] === 'USD' ? '$' : '€';
            if ($_SESSION['current_currency'] === 'USD') { return $currencySign . $attrs['value']; }
            return $currencySign . round((float)$base_rate  * (float)$attrs['value']);
        }
    }

    private function _convert($value, float $usd_curr_rate, float $base_rate_eur): string
    {
        $currencySign = $_SESSION['current_currency'] === 'USD' ? '$' : '€';
        if ($_SESSION['current_currency'] === 'USD') {
            // Convert source currency to USD: (1 / usd_curr_rate) * value
            return $currencySign . round(( 1 / $usd_curr_rate) * (float)$value);
        }
        // Convert source currency to EUR via USD: ((1 / usd_curr_rate) * value) * base_rate_eur
        // This converts: Source -> USD -> EUR
        return $currencySign . round(((1 / $usd_curr_rate) * (float)$value) * $base_rate_eur);
    }

    public function add_price_isk_eur_shortcode($attrs)
    {
        if (isset($attrs['value'])) {
            $options = get_option('openexchange_rate_options');
            $base_rate = $options['usd_isk_rate'];
            $base_rate_eur = $options['usd_eur_rate'];
            return $this->_convert($attrs['value'], (float)$base_rate, (float)$base_rate_eur);

        }
    }

    public function add_price_nok_eur_shortcode($attrs){
        if (isset($attrs['value'])) {
            $options = get_option('openexchange_rate_options');
            $base_rate = $options['usd_nok_rate'];
            $base_rate_eur = $options['usd_eur_rate'];
            return $this->_convert($attrs['value'], (float)$base_rate, (float)$base_rate_eur);
        }
    }

    public function add_price_sek_eur_shortcode($attrs){
        if (isset($attrs['value'])) {
            $options = get_option('openexchange_rate_options');
            $base_rate = $options['usd_sek_rate'];
            $base_rate_eur = $options['usd_eur_rate'];
            return $this->_convert($attrs['value'], (float)$base_rate, (float)$base_rate_eur);
        }
    }

    public function openexchange_rate_add_plugin_page() {
        add_menu_page(
            'OpenExchange Rate', // page_title
            'OpenExchange Rate', // menu_title
            'manage_options', // capability
            'openexchange-rate', // menu_slug
            array( $this, 'openexchange_rate_create_admin_page' ), // function
            'dashicons-admin-generic', // icon_url
            2 // position
        );
    }

    public function openexchange_rate_create_admin_page() {
        $this->openexchange_rate_options = get_option( 'openexchange_rate_options' );
        ?>
        <div class="wrap">
            <h2>OpenExchange Rate</h2>
            <p></p>
            <?php settings_errors(); ?>

            <form method="post" action="">
                <?php
                settings_fields( 'openexchange_rate_option_group' );
                do_settings_sections( 'openexchange-rate-admin' );
     //          submit_button();
                ?>
                <label for="date"></label>
                <button onclick="manualUpdate();">Manual API fetch</button>
                <button onclick="saveManualRate()">Save base rate</button>
                <button onclick="manualToursUpdate();">Manual tours update</button>
            </form>
        </div>

    <?php }

    public function openexchange_rate_page_init() {
        register_setting(
            'openexchange_rate_option_group', // option_group
            'openexchange_rate_options', // option_name
            array( $this, 'openexchange_rate_sanitize' ) // sanitize_callback
        );

        add_settings_section(
            'openexchange_rate_setting_section', // id
            'Settings', // title
            array( $this, 'openexchange_rate_section_info' ), // callback
            'openexchange-rate-admin' // page
        );

        add_settings_field(
            'usd_eur_rate', // id
            'USD/EUR Rate', // title
            array( $this, 'rate_usd_eur_callback' ), // callback
            'openexchange-rate-admin', // page
            'openexchange_rate_setting_section' // section
        );

        // Add SEK (Swedish Krona) rate field
        add_settings_field(
            'usd_sek_rate', // id
            'USD/SEK Rate', // title
            array( $this, 'rate_usd_sek_callback' ), // callback
            'openexchange-rate-admin', // page
            'openexchange_rate_setting_section' // section
        );

        // Add ISK (Icelandic Krona) rate field
        add_settings_field(
            'usd_isk_rate', // id
            'USD/ISK Rate', // title
            array( $this, 'rate_usd_isk_callback' ), // callback
            'openexchange-rate-admin', // page
            'openexchange_rate_setting_section' // section
        );

        // Add NOK (Norwegian Krone) rate field
        add_settings_field(
            'usd_nok_rate', // id
            'USD/NOK Rate', // title
            array( $this, 'rate_usd_nok_callback' ), // callback
            'openexchange-rate-admin', // page
            'openexchange_rate_setting_section' // section
        );


        add_settings_field(
                'datetime_fetched',
            'Last time fetched',
            array($this, 'datetime_callback'),
            'openexchange-rate-admin',
            'openexchange_rate_setting_section'
        );

        add_settings_field(
                'manual_exchange_rate',
            'Manual Exchange Rate',
            array($this, 'manual_exchange_rate_callback'),
            'openexchange-rate-admin',
            'openexchange_rate_setting_section'
        );

        add_settings_field(
            'manual_exchange_rate_sek',
            'Manual Exchange Rate (SEK)',
            array($this, 'manual_exchange_rate_sek_callback'),
            'openexchange-rate-admin',
            'openexchange_rate_setting_section'
        );

        // ISK (Icelandic Krona) manual exchange rate field
        add_settings_field(
            'manual_exchange_rate_isk',
            'Manual Exchange Rate (ISK)',
            array($this, 'manual_exchange_rate_isk_callback'),
            'openexchange-rate-admin',
            'openexchange_rate_setting_section'
        );

        // NOK (Norwegian Krone) manual exchange rate field
        add_settings_field(
            'manual_exchange_rate_nok',
            'Manual Exchange Rate (NOK)',
            array($this, 'manual_exchange_rate_nok_callback'),
            'openexchange-rate-admin',
            'openexchange_rate_setting_section'
        );

        add_settings_field(
                'manual_exchange_rate_updated_at',
            'Manual Exchange Rate last Updated',
            array($this, 'manual_exchange_rate_updated_at_callback'),
            'openexchange-rate-admin',
            'openexchange_rate_setting_section'
        );
    }

    public function openexchange_rate_sanitize($input) {
        $sanitary_values = array();
        if ( isset( $input['usd_eur_rate'] ) ) {
            $sanitary_values['usd_eur_rate'] = sanitize_text_field( $input['usd_eur_rate'] );
        }

        if ( isset( $input['usd_sek_rate'] ) ) {
            $sanitary_values['usd_sek_rate'] = sanitize_text_field( $input['usd_sek_rate'] );
        }

        if ( isset( $input['usd_isk_rate'] ) ) {
            $sanitary_values['usd_isk_rate'] = sanitize_text_field( $input['usd_isk_rate'] );
        }

        if ( isset( $input['usd_nok_rate'] ) ) {
            $sanitary_values['usd_nok_rate'] = sanitize_text_field( $input['usd_nok_rate'] );
        }

        if (isset( $input['manual_exchange_rate'] ) ) {
            $sanitary_values['manual_exchange_rate'] = sanitize_text_field( $input['manual_exchange_rate'] );
        }

        if (isset( $input['manual_exchange_rate_sek'] ) ) {
            $sanitary_values['manual_exchange_rate_sek'] = sanitize_text_field( $input['manual_exchange_rate_sek'] );
        }

        if (isset( $input['manual_exchange_rate_nok'] ) ) {
            $sanitary_values['manual_exchange_rate_nok'] = sanitize_text_field( $input['manual_exchange_rate_nok'] );
        }

        if (isset( $input['manual_exchange_rate_isk'] ) ) {
            $sanitary_values['manual_exchange_rate_isk'] = sanitize_text_field( $input['manual_exchange_rate_isk'] );
        }
        
        if (isset( $input['manual_exchange_rate_updated_at'] ) ) {
            $sanitary_values['manual_exchange_rate_updated_at'] = sanitize_text_field( $input['manual_exchange_rate_updated_at'] );
        }

        if ( isset( $input['datetime_fetched'] ) ) {
            $sanitary_values['datetime_fetched'] = sanitize_text_field( $input['datetime_fetched'] );
        }

        return $sanitary_values;
    }


    public function openexchange_rate_section_info() {

    }

    public function manual_exchange_rate_callback() {
            printf(
                    '<input class="regular-text manual-exchange-rate" type="text" name="openexchange_rate_options[manual_exchange_rate]" value="%s" />',
                isset($this->openexchange_rate_options['manual_exchange_rate']) ? esc_attr($this->openexchange_rate_options['manual_exchange_rate']): ''
            );
    }

    public function manual_exchange_rate_sek_callback() {
        printf(
            '<input type="text" class="regular-text manual_exchange_rate_sek" id="manual_exchange_rate_sek" name="openexchange_rate_options[manual_exchange_rate_sek]" value="%s" />',
            isset($this->openexchange_rate_options['manual_exchange_rate_sek']) ? esc_attr($this->openexchange_rate_options['manual_exchange_rate_sek']) : ''
        );
    }

    public function manual_exchange_rate_isk_callback() {
        printf(
            '<input type="text" class="regular-text manual_exchange_rate_isk"  id="manual_exchange_rate_isk" name="openexchange_rate_options[manual_exchange_rate_isk]" value="%s" />',
            isset($this->openexchange_rate_options['manual_exchange_rate_isk']) ? esc_attr($this->openexchange_rate_options['manual_exchange_rate_isk']) : ''
        );
    }

    public function manual_exchange_rate_nok_callback() {
        printf(
            '<input type="text" class="regular-text manual_exchange_rate_nok"  id="manual_exchange_rate_nok" name="openexchange_rate_options[manual_exchange_rate_nok]" value="%s" />',
            isset($this->openexchange_rate_options['manual_exchange_rate_nok']) ? esc_attr($this->openexchange_rate_options['manual_exchange_rate_nok']) : ''
        );
    }

    public function rate_usd_eur_callback() {
        printf(
            '<input class="regular-text" type="text" name="openexchange_rate_options[usd_eur_rate]" disabled id="usd_eur_rate" value="%s">',
            isset( $this->openexchange_rate_options['usd_eur_rate'] ) ? esc_attr( $this->openexchange_rate_options['usd_eur_rate']) : ''
        );
    }

    public function rate_usd_sek_callback() {
        printf(
            '<input class="regular-text" type="text" id="usd_sek_rate" name="openexchange_rate_options[usd_sek_rate]" disabled value="%s" />',
            isset( $this->openexchange_rate_options['usd_sek_rate'] ) ? esc_attr( $this->openexchange_rate_options['usd_sek_rate']) : ''
        );
    }

    public function rate_usd_isk_callback() {
        printf(
            '<input class="regular-text" type="text" id="usd_isk_rate" name="openexchange_rate_options[usd_isk_rate]" disabled value="%s" />',
            isset( $this->openexchange_rate_options['usd_isk_rate'] ) ? esc_attr( $this->openexchange_rate_options['usd_isk_rate']) : ''
        );
    }

    public function rate_usd_nok_callback() {
        printf(
            '<input class="regular-text" type="text" id="usd_nok_rate" name="openexchange_rate_options[usd_nok_rate]" disabled value="%s" />',
            isset( $this->openexchange_rate_options['usd_nok_rate'] ) ? esc_attr( $this->openexchange_rate_options['usd_nok_rate']) : ''
        );
    }
    

    public function manual_exchange_rate_updated_at_callback() {
        printf(
            '<strong><div>%s</div></strong>',
            isset($this->openexchange_rate_options['manual_exchange_rate_updated_at']) ? date('Y-m-d H:i:s' , $this->openexchange_rate_options['manual_exchange_rate_updated_at'] ): ''
        );
    }

    /**
     * @return void
     */
    public function datetime_callback() {
        printf(
                '<strong><div>%s</div></strong>',
                isset($this->openexchange_rate_options['datetime_fetched']) ? date('Y-m-d H:i:s' , $this->openexchange_rate_options['datetime_fetched']): ''
        );
    }

    /**
     * @param $options
     * @return mixed
     */
    public static function add_plugin_option($options)
    {
        $options['tourmaster-exchange'] = array(
            'title' => esc_html__('Exchange Rate API', 'tourmaster'),
            'options' => array(
                'enable-exchange' => array(
                    'title' => esc_html__('Enable Exchange Rate', 'tourmaster'),
                    'type'  => 'checkbox',
                    'default' => 'enable'
                ),
                'exchange-api-key' => array(
                    'title' => esc_html__('OpenExchangeRates.org API ID'),
                    'type'  => 'text',
                    'description' => 'API For Open Exchange API'
                ),
            ));

        return $options;
    }

    /**
     * @param $options
     * @return array
     */
    public static function add_tour_option($options)
    { 
        if (isset($options['tour-settings']) && $options['tour-settings'] !== null) {
            $options['tour-settings']['options'] = array_merge($options['tour-settings']['options'], array(
                'enable-manual-conversion' => array(
                    'title' => esc_html__('Enable Manual Rate conversion', 'tourmaster'),
                    'type' => 'checkbox',
                    'default' => 'disable'
                )
            ));

        /*    $options['tour-settings']['options'] = array_merge($options['tour-settings']['options'], array(
                'enable-manual-sek-conversion' => array(
                    'title' => esc_html__('Enable Manual Rate conversion (SEK)', 'tourmaster'),
                    'type' => 'checkbox',
                    'default' => 'disable'
                )
            ));

            $options['tour-settings']['options'] = array_merge($options['tour-settings']['options'], array(
                'enable-manual-isk-conversion' => array(
                    'title' => esc_html__('Enable Manual Rate conversion (ISK)', 'tourmaster'),
                    'type' => 'checkbox',
                    'default' => 'disable'
                )
            ));

            $options['tour-settings']['options'] = array_merge($options['tour-settings']['options'], array(
                'enable-manual-nok-conversion' => array(
                    'title' => esc_html__('Enable Manual Rate conversion (NOK)', 'tourmaster'),
                    'type' => 'checkbox',
                    'default' => 'disable'
                )
            ));*/ 
        }

        return $options;
    }

    /**
     * @return void
     */
    public function register_all_scripts(){
        add_action( 'admin_enqueue_scripts', [$this, 'enqueueJavascriptFile'] );
    }

    /**
     * @return void
     */
    public function enqueueJavascriptFile() {
        wp_enqueue_script('zcustom-tourmaster-openexchanges', CUSTOM_TOURMASTER_OPENEXCHANGE_URL . '/js/custom-tourmaster-openexchangerates.js', array('jquery'), '1.0', true);
        wp_enqueue_style('zcustom-tourmaster-openexchanges-style', CUSTOM_TOURMASTER_OPENEXCHANGE_URL . '/css/custom-tourmaster-openexchangerates.css');
        // localize the script to your domain name, so that you can reference the url to admin-ajax.php file easily
        wp_localize_script( 'zcustom-tourmaster-openexchanges', 'ajax', array( 'ajaxurl' => admin_url( 'admin-ajax.php' )));
    }

    public function fetch_base_rate()
    {
        if (!isset($_POST["postId"])) {
            return false;
        }

        $postId = sanitize_text_field($_POST["postId"]);
        $currency = static::get_post_currency($postId);

        $options = get_option('openexchange_rate_options');
        // get recent saved value
        if ((int)$options["manual_exchange_rate_updated_at"] > (int)$options["datetime_fetched"]) {
            $baseRate = $options["manual_exchange_rate"];
            echo wp_json_encode(['success' => true, 'rate' => $baseRate]);
            die;
        }

        $baseRate = $options["usd_eur_rate"];
        if (empty($currency)) {
            $currency = strtolower($currency);
            $baseRate = $options["usd_{$currency}_rate"];

            if (in_array($currency, ['isk', 'sek', 'nok'])) {
                $baseRate = 1 / $baseRate;
            }
        }

        echo wp_json_encode(['success' => true, 'rate' => $baseRate]);
        die;
    }

}

function initCustomTourmasterOpenExchangesRates() {
    return new Custom_Tourmaster_OpenExchange();
}

register_activation_hook(__FILE__, 'wp_custom_tourmaster_exchange_activation_hourly_event');
register_deactivation_hook( __FILE__, 'wp_custom_tourmaster_exhcange_deactivate_recurring_events' );
function wp_custom_tourmaster_exchange_activation_hourly_event() {

    if (!wp_next_scheduled( 'custom_tourmaster_openexchangerates_run_cron')) {
        wp_schedule_event(time(), 'hourly', 'custom_tourmaster_openexchangerates_run_cron');
    }

    $instance = initCustomTourmasterOpenExchangesRates();
    $instance->create_menu_entries();
}

function wp_custom_tourmaster_exhcange_deactivate_recurring_events() {
    wp_clear_scheduled_hook( 'custom_tourmaster_openexchangerates_run_cron' );
}

add_action('custom_tourmaster_openexchangerates_run_cron', function() {
    OpenExchangeRates::hitCron();
    error_log('Cronjob: check bestmeing tours started');
    check_bestmeing_tours_prices();
});


/// OpenExchangeRates::hitCron();

add_filter('goodlayers_plugin_payment_option', 'Custom_Tourmaster_OpenExchange::add_plugin_option');
add_filter('tourmaster_tour_options', 'Custom_Tourmaster_OpenExchange::add_tour_option');
add_action('init', 'initCustomTourmasterOpenExchangesRates');
