<?php
/**
 * Test exact matching between shortcode and tour price conversion
 */

// Mock WordPress functions
function get_option($option_name) {
    return [
        'usd_isk_rate' => 138.50,  // 1 USD = 138.50 ISK
        'usd_eur_rate' => 0.92     // 1 USD = 0.92 EUR
    ];
}

function get_post_meta($post_id, $meta_key, $single = false) {
    // Mock tour data with USD prices (converted from 24,900 ISK)
    return [
        'tour-price-text' => '180',  // $180 USD (from 24,900 ISK)
        'date-price' => [
            [
                'package' => [
                    [
                        'adult-price' => '180',      // $180 USD
                        'children-price' => '90',    // $90 USD
                        'person-price' => '180'      // $180 USD
                    ]
                ]
            ]
        ]
    ];
}

function get_the_terms($post_id, $taxonomy) {
    // Mock taxonomy term with ISK currency
    return [
        (object)[
            'term_id' => 1,
            'name' => 'Iceland'
        ]
    ];
}

function get_term_meta($term_id, $meta_key, $single = false) {
    return 'ISK'; // Mock currency
}

function is_wp_error($thing) {
    return false; // Mock WordPress function
}

// Start session
session_start();

// Include OpenExchangeRates class
class OpenExchangeRates {
    public static function convert(float $base, float $fromValue) {
        return round($base * $fromValue);
    }
}

// Mock get_post_currency method
function get_post_currency($post_id, $taxonomy_slug = 'bestemming') {
    $terms = get_the_terms($post_id, $taxonomy_slug);
    if (!empty($terms) && !is_wp_error($terms)) {
        $term = $terms[0];
        $currency = get_term_meta($term->term_id, 'currency', true);
        return !empty($currency) ? $currency : 'EUR';
    }
    return 'EUR';
}

// Shortcode conversion method
function _convert($value, float $usd_curr_rate, float $base_rate_eur): string
{
    $currencySign = $_SESSION['current_currency'] === 'USD' ? '$' : '€';
    if ($_SESSION['current_currency'] === 'USD') { 
        return $currencySign . round(( 1 / $usd_curr_rate) * (float)$value); 
    }
    return $currencySign . round(((1 / $usd_curr_rate) * (float)$value) * $base_rate_eur);
}

function add_price_isk_eur_shortcode($attrs) {
    if (isset($attrs['value'])) {
        $options = get_option('openexchange_rate_options');
        $base_rate = $options['usd_isk_rate'];
        $base_rate_eur = $options['usd_eur_rate'];
        return _convert($attrs['value'], (float)$base_rate, (float)$base_rate_eur);
    }
}

// NEW tour price conversion method (using shortcode logic)
function convert_tour_prices_with_shortcode_logic($current_meta, $object_id) {
    $options = get_option('openexchange_rate_options');
    $usd_eur_rate = $options['usd_eur_rate'];
    
    // Get the tour's original currency
    $tour_currency = get_post_currency($object_id);
    
    if (in_array(strtolower($tour_currency), ['isk', 'sek', 'nok'])) {
        // For ISK/SEK/NOK tours: Convert back to original currency, then use shortcode logic
        $usd_curr_rate = $options["usd_" . strtolower($tour_currency) . "_rate"];
        
        // Convert main tour price using shortcode logic
        if (!empty($current_meta['tour-price-text'])) {
            // Convert USD back to original currency, then to EUR using shortcode formula
            $original_value = (float)$current_meta['tour-price-text'] * $usd_curr_rate;
            $eur_value = round(((1 / $usd_curr_rate) * $original_value) * $usd_eur_rate);
            $current_meta['tour-price-text'] = (string)$eur_value;
        }
        
        // Convert package prices using shortcode logic
        if (isset($current_meta['date-price'][0]['package'])) {
            $date_price = $current_meta['date-price'][0];
            foreach ($date_price['package'] as $index => $package) {
                foreach($package as $key => $item) {
                    if (str_ends_with($key, 'price') && !empty($item)) {
                        // Convert USD back to original currency, then to EUR using shortcode formula
                        $original_value = (float)$item * $usd_curr_rate;
                        $eur_value = round(((1 / $usd_curr_rate) * $original_value) * $usd_eur_rate);
                        $current_meta['date-price'][0]['package'][$index][$key] = (string)$eur_value;
                    }
                }
            }
        }
    }
    
    return $current_meta;
}

echo "=== Exact Matching Test: 24,900 ISK ===\n";
$options = get_option('openexchange_rate_options');
echo "Exchange rates:\n";
echo "- 1 USD = {$options['usd_isk_rate']} ISK\n";
echo "- 1 USD = {$options['usd_eur_rate']} EUR\n\n";

// Test shortcode conversion
echo "🔸 Shortcode Conversion:\n";
$_SESSION['current_currency'] = 'EUR';
$shortcode_result = add_price_isk_eur_shortcode(['value' => 24900]);
echo "  [price_isk_eur value=\"24900\"] = {$shortcode_result}\n\n";

// Test NEW tour price conversion
echo "🔸 Tour Price Conversion (NEW logic):\n";
$tour_meta = get_post_meta(123, 'tourmaster-tour-option', true);

echo "  Original USD prices:\n";
echo "    - Tour price: \${$tour_meta['tour-price-text']}\n";
echo "    - Adult price: \${$tour_meta['date-price'][0]['package'][0]['adult-price']}\n\n";

$converted_meta = convert_tour_prices_with_shortcode_logic($tour_meta, 123);

echo "  Converted EUR prices (using shortcode logic):\n";
echo "    - Tour price: €{$converted_meta['tour-price-text']}\n";
echo "    - Adult price: €{$converted_meta['date-price'][0]['package'][0]['adult-price']}\n\n";

// Compare results
$shortcode_eur_value = (int)str_replace('€', '', $shortcode_result);
$tour_eur_value = (int)$converted_meta['tour-price-text'];

echo "=== Exact Match Test ===\n";
echo "Shortcode result: €{$shortcode_eur_value}\n";
echo "Tour price result: €{$tour_eur_value}\n";
echo "Exact match: " . ($shortcode_eur_value === $tour_eur_value ? "✅ YES - PERFECT!" : "❌ NO") . "\n\n";

// Show the math
echo "=== Math Verification ===\n";
echo "Both use same formula: ((1 ÷ 138.5) × 24900) × 0.92\n";
$step1 = (1 / 138.5) * 24900;
$step2 = $step1 * 0.92;
echo "= ({$step1}) × 0.92\n";
echo "= {$step2}\n";
echo "= " . round($step2) . " EUR (rounded)\n";
