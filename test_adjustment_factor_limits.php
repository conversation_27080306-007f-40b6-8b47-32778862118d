<?php
/**
 * Test if the adjustment factor works for different values
 */

function test_conversion($isk_value, $description) {
    $usd_isk_rate = 138.50;
    $usd_eur_rate = 0.92;
    $adjustment_factor = 0.9963768115942;
    
    // Shortcode calculation (exact)
    $shortcode_eur = round(((1 / $usd_isk_rate) * $isk_value) * $usd_eur_rate);
    
    // What gets stored as USD (rounded)
    $stored_usd = round((1 / $usd_isk_rate) * $isk_value);
    
    // Simple USD to EUR
    $simple_eur = round($stored_usd * $usd_eur_rate);
    
    // Adjusted USD to EUR
    $adjusted_eur = round($stored_usd * $usd_eur_rate * $adjustment_factor);
    
    echo "=== {$description} ===\n";
    echo "ISK value: " . number_format($isk_value) . "\n";
    echo "Stored USD: \${$stored_usd}\n";
    echo "Shortcode result: €{$shortcode_eur}\n";
    echo "Simple conversion: €{$simple_eur}\n";
    echo "Adjusted conversion: €{$adjusted_eur}\n";
    echo "Shortcode vs Adjusted: " . ($shortcode_eur === $adjusted_eur ? "✅ MATCH" : "❌ NO MATCH") . "\n";
    echo "Difference: " . abs($shortcode_eur - $adjusted_eur) . " EUR\n\n";
}

echo "Testing adjustment factor 0.9963768115942 with different values:\n\n";

// Test various ISK values
test_conversion(24900, "Original case (24,900 ISK)");
test_conversion(10000, "Smaller value (10,000 ISK)");
test_conversion(50000, "Larger value (50,000 ISK)");
test_conversion(5000, "Small value (5,000 ISK)");
test_conversion(100000, "Large value (100,000 ISK)");
test_conversion(1000, "Very small (1,000 ISK)");

echo "=== CONCLUSION ===\n";
echo "The adjustment factor 0.9963768115942 was designed specifically for the 24,900 ISK case.\n";
echo "It will NOT work correctly for other values due to different rounding behaviors.\n";
